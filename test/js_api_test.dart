import 'package:flutter_test/flutter_test.dart';
import 'package:app_rhyme/types/extern_api.dart';
import 'dart:io';

void main() {
  group('JavaScript API Structure Tests', () {
    test('JsApi class creation from path', () async {
      // 创建测试JavaScript文件
      var testJsPath = 'test_js_source_temp.js';
      var testJsFile = File(testJsPath);

      try {
        await testJsFile.writeAsString('''
function getMusicPlayInfo(source, extra) {
    return {
        qualities: [
            {
                summary: "高品质",
                bitrate: 320,
                format: "mp3",
                size: 8000000,
                url: "https://example.com/test.mp3"
            }
        ]
    };
}
        ''');

        var jsApi = await JsApi.fromPath(path: testJsPath);

        expect(jsApi.localPath, equals(testJsPath));
        expect(jsApi.lastHash, isNotNull);
        expect(jsApi.lastModifiedTime, isNotNull);
        expect(jsApi.url, isNull); // 从路径创建时url应该为null

      } finally {
        // 清理测试文件
        if (await testJsFile.exists()) {
          await testJsFile.delete();
        }
      }
    });

    test('JsApi different files have different hashes', () async {
      var testJsPath1 = 'test_js_source_temp1.js';
      var testJsPath2 = 'test_js_source_temp2.js';
      var testJsFile1 = File(testJsPath1);
      var testJsFile2 = File(testJsPath2);

      try {
        await testJsFile1.writeAsString('function test() { return 1; }');
        await testJsFile2.writeAsString('function test() { return 2; }');

        var jsApi1 = await JsApi.fromPath(path: testJsPath1);
        var jsApi2 = await JsApi.fromPath(path: testJsPath2);

        expect(jsApi1.lastHash, isNotEmpty);
        expect(jsApi2.lastHash, isNotEmpty);
        expect(jsApi1.lastHash, isNot(equals(jsApi2.lastHash))); // 不同内容应该有不同的hash

      } finally {
        if (await testJsFile1.exists()) await testJsFile1.delete();
        if (await testJsFile2.exists()) await testJsFile2.delete();
      }
    });

    test('JsApi equality comparison', () async {
      var testJsPath = 'test_js_source_temp2.js';
      var testJsFile = File(testJsPath);

      try {
        await testJsFile.writeAsString('function test() {}');

        var jsApi1 = await JsApi.fromPath(path: testJsPath);
        var jsApi2 = await JsApi.fromPath(path: testJsPath);

        expect(jsApi1, equals(jsApi2));

      } finally {
        if (await testJsFile.exists()) {
          await testJsFile.delete();
        }
      }
    });
  });
}
