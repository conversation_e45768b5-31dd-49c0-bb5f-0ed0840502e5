/**
 * KXZ NetEase Music JS Source
 * Based on kxzjoker.cn API
 * Author: johke
 * Version: 1.0.0
 */

// Configuration
const CONFIG = {
  name: 'KXZ NetEase Music',
  version: '1.0.0',
  author: 'johke',
  description: 'NetEase Music source based on kxzjoker.cn API',
  baseUrl: 'https://api.kxzjoker.cn'
};

/**
 * HTTP request helper function
 */
async function httpRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://music.163.com/',
        ...options.headers
      },
      body: options.body
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Request failed: ${error.message}`);
    throw error;
  }
}

/**
 * Parse extra parameters
 * @param {string} extra - Extra parameter string (ID or JSON)
 * @returns {Object} Parsed parameters
 */
function _parseExtraParams(extra) {
  try {
    // Try to parse JSON format extra parameter
    if (extra.startsWith('{')) {
      return JSON.parse(extra);
    }

    // If it's a simple ID, return basic object
    return {
      id: extra,
      quality: 'standard'
    };
  } catch (e) {
    return {
      id: extra,
      quality: 'standard'
    };
  }
}

/**
 * Search music
 * @param {string} keyword - Search keyword
 * @param {number} limit - Result limit
 * @returns {Promise<Object>} Search results
 */
async function searchMusic(keyword, limit = 10) {
  try {
    console.log(`Search music: ${keyword}, limit: ${limit}`);

    const url = `${CONFIG.baseUrl}/api/163_search?name=${encodeURIComponent(keyword)}&limit=${limit}`;
    const response = await httpRequest(url);

    if (!response.data || response.code !== 200) {
      return {
        musics: [],
        total: 0,
        page: 1
      };
    }

    const musics = response.data.map(song => {
      // 解析时长 "03:22" -> 毫秒
      let durationMs = 0;
      if (song.duration) {
        const parts = song.duration.split(':');
        if (parts.length === 2) {
          durationMs = (parseInt(parts[0]) * 60 + parseInt(parts[1])) * 1000;
        }
      }

      return {
        id: song.id.toString(),
        name: song.name,
        artist: song.artists.map(artist => artist.name).join(', '),
        album: song.album.name,
        duration: durationMs,
        pic_url: `https://p1.music.126.net/${song.id}/109951167791456650.jpg`, // 默认封面
        source: 'js'
      };
    });

    return {
      musics: musics,
      total: response.data.length,
      page: 1
    };

  } catch (error) {
    console.error(`Search music failed: ${error.message}`);
    return {
      musics: [],
      total: 0,
      page: 1
    };
  }
}

/**
 * Get music play information
 * @param {string} source - Music source JSON string
 * @param {string} extra - Extra information (contains quality level)
 * @returns {Promise<Object|null>} Play information
 */
async function getMusicPlayInfo(source, extra = '{}') {
  try {
    console.log(`Get play info: source=${source}, extra=${extra}`);

    const musicInfo = JSON.parse(source);
    const musicId = musicInfo.id;

    // 解析extra参数获取音质等级
    const extraParams = _parseExtraParams(extra);
    const level = extraParams.quality || extraParams.level || 'standard';

    // 使用网易云音乐播放链接API，支持音质参数
    const url = `${CONFIG.baseUrl}/api/163_url?id=${musicId}&level=${level}`;
    const response = await httpRequest(url);

    if (!response.data || response.code !== 200) {
      throw new Error('Unable to get play URL');
    }

    const songData = response.data;
    if (!songData.url) {
      throw new Error('Song cannot be played (may require VIP)');
    }

    // 根据level和比特率确定音质描述
    const bitrate = songData.br || 128000;
    let qualitySummary = '标准';

    switch (level) {
      case 'standard':
        qualitySummary = '标准';
        break;
      case 'exhigh':
        qualitySummary = '极高品质';
        break;
      case 'lossless':
        qualitySummary = '无损音质';
        break;
      case 'hires':
        qualitySummary = 'Hi-Res音质';
        break;
      case 'jyeffect':
        qualitySummary = '高清环绕声';
        break;
      case 'sky':
        qualitySummary = '沉浸环绕声';
        break;
      case 'jymaster':
        qualitySummary = '超清母带';
        break;
      default:
        if (bitrate >= 320000) {
          qualitySummary = '极高品质';
        }
    }

    return {
      qualities: [
        {
          summary: qualitySummary,
          bitrate: bitrate,
          format: songData.type || 'mp3',
          size: songData.size || 0,
          url: songData.url
        }
      ]
    };

  } catch (error) {
    console.error(`Get play info failed: ${error.message}`);
    return null;
  }
}

/**
 * Get lyric information
 * @param {string} source - Music source JSON string
 * @param {string} extra - Extra information
 * @returns {Promise<Object|null>} Lyric information
 */
async function getLyric(source, extra = '{}') {
  try {
    console.log(`Get lyrics: source=${source}, extra=${extra}`);

    const musicInfo = JSON.parse(source);
    const musicId = musicInfo.id;

    const url = `${CONFIG.baseUrl}/api/163_lyric?id=${musicId}`;
    const response = await httpRequest(url);

    if (!response.data || response.code !== 200) {
      return {
        lyric: '[00:00.00]暂无歌词',
        tlyric: null
      };
    }

    return {
      lyric: response.data.lrc || '[00:00.00]暂无歌词',
      tlyric: response.data.tlyric || null
    };

  } catch (error) {
    console.error(`Get lyrics failed: ${error.message}`);
    return {
      lyric: '[00:00.00]获取歌词失败',
      tlyric: null
    };
  }
}

/**
 * Get available qualities
 * @returns {Array} Available quality options
 */
function getAvailableQualities() {
  return [
    {
      summary: "标准",
      bitrate: 128,
      format: "mp3",
      level: "standard"
    },
    {
      summary: "极高品质",
      bitrate: 320,
      format: "mp3",
      level: "exhigh"
    },
    {
      summary: "无损音质",
      bitrate: 999,
      format: "flac",
      level: "lossless"
    },
    {
      summary: "Hi-Res音质",
      bitrate: 1411,
      format: "flac",
      level: "hires"
    },
    {
      summary: "高清环绕声",
      bitrate: 320,
      format: "mp3",
      level: "jyeffect"
    },
    {
      summary: "沉浸环绕声",
      bitrate: 320,
      format: "mp3",
      level: "sky"
    },
    {
      summary: "超清母带",
      bitrate: 1411,
      format: "flac",
      level: "jymaster"
    }
  ];
}

// Export functions for Flutter to call
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    CONFIG,
    searchMusic,
    getMusicPlayInfo,
    getLyric,
    getAvailableQualities
  };
}

console.log(`${CONFIG.name} v${CONFIG.version} loaded successfully`);
