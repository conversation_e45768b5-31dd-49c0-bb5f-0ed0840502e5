import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_rhyme/dialogs/confirm_dialog.dart';

import 'package:app_rhyme/dialogs/quality_select_dialog.dart';
import 'package:app_rhyme/dialogs/wait_dialog.dart';
import 'package:app_rhyme/src/rust/api/bind/factory_bind.dart';
import 'package:app_rhyme/src/rust/api/cache/fs_util.dart';

import 'package:app_rhyme/utils/cache_helper.dart';

import 'package:app_rhyme/utils/chore.dart';
import 'package:app_rhyme/utils/colors.dart';
import 'package:app_rhyme/utils/const_vars.dart';
import 'package:app_rhyme/types/extern_api.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:app_rhyme/utils/quality_picker.dart';
import 'package:app_rhyme/utils/refresh.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';

import 'package:pull_down_button/pull_down_button.dart';
import 'package:talker_flutter/talker_flutter.dart';

class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  MorePageState createState() => MorePageState();
}

class MorePageState extends State<MorePage> with WidgetsBindingObserver {
  refresh() {
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final brightness = MediaQuery.of(context).platformBrightness;
    final textColor = brightness == Brightness.dark
        ? CupertinoColors.white
        : CupertinoColors.black;
    final iconColor = brightness == Brightness.dark
        ? CupertinoColors.white
        : CupertinoColors.black;
    final backgroundColor = brightness == Brightness.dark
        ? CupertinoColors.systemGrey6
        : CupertinoColors.systemGroupedBackground;
    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        leading: Padding(
          padding: const EdgeInsets.only(left: 0.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '设置',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 24,
                color: textColor,
              ).useSystemChineseFont(),
            ),
          ),
        ),
      ),
      child: ListView(
        children: [
          CupertinoFormSection.insetGrouped(
            header: Text('应用信息',
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                  prefix: SizedBox(
                      height: 60,
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: imageCacheHelper(""),
                      )),
                  child: Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        padding: const EdgeInsets.only(left: 10),
                        child: Text(
                          'AppRhyme',
                          style: TextStyle(
                            color: textColor,
                            fontSize: 20.0,
                          ).useSystemChineseFont(),
                        ),
                      ))),
              CupertinoFormRow(
                  prefix: Padding(
                      padding: const EdgeInsets.only(right: 20),
                      child: Text(
                        '版本号',
                        style:
                            TextStyle(color: textColor).useSystemChineseFont(),
                      )),
                  child: Container(
                      padding: const EdgeInsets.only(right: 10),
                      alignment: Alignment.centerRight,
                      height: 40,
                      child: Text(
                        globalPackageInfo.version,
                        style:
                            TextStyle(color: textColor).useSystemChineseFont(),
                      ))),
              // 更新检查功能已被禁用
              // CupertinoFormRow(
              //   prefix: Text(
              //     '检查更新',
              //     style: TextStyle(color: textColor).useSystemChineseFont(),
              //   ),
              //   child: CupertinoButton(
              //     onPressed: () async {
              //       await checkVersionUpdate(context, true);
              //     },
              //     child: Icon(CupertinoIcons.cloud, color: iconColor),
              //   ),
              // ),
              // CupertinoFormRow(
              //   prefix: Padding(
              //       padding: const EdgeInsets.only(right: 20),
              //       child: Text(
              //         '自动检查更新',
              //         style: TextStyle(color: textColor).useSystemChineseFont(),
              //       )),
              //   child: CupertinoSwitch(
              //       value: globalConfig.versionAutoUpdate,
              //       onChanged: (value) {
              //         if (value != globalConfig.versionAutoUpdate) {
              //           globalConfig.versionAutoUpdate = value;
              //           globalConfig.save();
              //           setState(() {});
              //         }
              //       }),
              // ),
              CupertinoFormRow(
                prefix: Text(
                  '项目链接',
                  style: TextStyle(color: textColor).useSystemChineseFont(),
                ),
                child: CupertinoButton(
                  onPressed: openProjectLink,
                  child: Text(
                    'github.com/canxin121/app_rhyme',
                    style: TextStyle(color: textColor).useSystemChineseFont(),
                  ),
                ),
              ),
            ],
          ),
          CupertinoFormSection.insetGrouped(
            header: Text("音频设置",
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                  prefix: Text("清空待播清单",
                      style:
                          TextStyle(color: textColor).useSystemChineseFont()),
                  child: CupertinoButton(
                      child: Icon(
                        CupertinoIcons.clear,
                        color: activeIconRed,
                      ),
                      onPressed: () {
                        globalAudioHandler.clear();
                      }))
            ],
          ),
          _buildExternApiSection(textColor, iconColor),
          _buildQualitySelectSection(context, () {
            setState(() {});
          }, textColor),
          // IOS系统无法直接访问文件系统，且已开启在文件中显示应用数据，所以不显示此选项
          if (!Platform.isIOS)
            _buildExportCacheRoot(context, refresh, textColor, iconColor),
          CupertinoFormSection.insetGrouped(
            header: Text('储存设置',
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                prefix: Padding(
                    padding: const EdgeInsets.only(right: 20),
                    child: Text(
                      '保存歌曲时缓存歌曲封面',
                      style: TextStyle(color: textColor).useSystemChineseFont(),
                    )),
                child: CupertinoSwitch(
                    value: globalConfig.savePicWhenAddMusicList,
                    onChanged: (value) {
                      if (value != globalConfig.savePicWhenAddMusicList) {
                        globalConfig.savePicWhenAddMusicList = value;
                        globalConfig.save();
                        setState(() {});
                      }
                    }),
              ),
              CupertinoFormRow(
                prefix: Padding(
                    padding: const EdgeInsets.only(right: 20),
                    child: Text(
                      '保存歌单时缓存歌曲歌词',
                      style: TextStyle(color: textColor).useSystemChineseFont(),
                    )),
                child: CupertinoSwitch(
                    value: globalConfig.saveLyricWhenAddMusicList,
                    onChanged: (value) {
                      if (value != globalConfig.saveLyricWhenAddMusicList) {
                        globalConfig.saveLyricWhenAddMusicList = value;
                        globalConfig.save();
                        setState(() {});
                      }
                    }),
              ),
              CupertinoFormRow(
                  prefix: Text("清除冗余歌曲数据",
                      style:
                          TextStyle(color: textColor).useSystemChineseFont()),
                  child: CupertinoButton(
                      onPressed: () async {
                        try {
                          await SqlFactoryW.cleanUnusedMusicData();
                          await SqlFactoryW.cleanUnusedMusiclist();
                          refreshMusicListGridViewPage();
                          LogToast.success("储存清理", "清理无用歌曲数据成功",
                              "[MorePage] Cleaned unused music data");
                        } catch (e) {
                          LogToast.error("储存清理", "清理无用歌曲数据失败: $e",
                              "[MorePage] Failed to clean unused music data: $e");
                        }
                      },
                      child: const Icon(
                        CupertinoIcons.bin_xmark,
                        color: CupertinoColors.systemRed,
                      ))),
            ],
          ),
          CupertinoFormSection.insetGrouped(
            header: Text('其他',
                style: TextStyle(color: textColor).useSystemChineseFont()),
            children: [
              CupertinoFormRow(
                  prefix: Text("运行日志",
                      style:
                          TextStyle(color: textColor).useSystemChineseFont()),
                  child: CupertinoButton(
                      child: const Icon(
                        CupertinoIcons.book,
                        color: CupertinoColors.activeGreen,
                      ),
                      onPressed: () {
                        Navigator.of(context).push(CupertinoPageRoute(
                          builder: (context) =>
                              TalkerScreen(talker: globalTalker),
                        ));
                      })),
            ],
          ),
        ],
      ),
    );
  }

  CupertinoFormSection _buildExternApiSection(
      Color textColor, Color iconColor) {
    bool hasExternApi = globalJsApiEvaler != null;
    List<Widget> children = [];
    if (hasExternApi) {
      children.add(CupertinoFormRow(
          prefix: Padding(
              padding: const EdgeInsets.only(right: 20),
              child: Text(
                '音源状态',
                style: TextStyle(color: textColor).useSystemChineseFont(),
              )),
          child: Container(
              padding: const EdgeInsets.only(right: 10),
              alignment: Alignment.centerRight,
              height: 50,
              child: Text(
                "正常",
                style: const TextStyle(color: CupertinoColors.activeGreen)
                    .useSystemChineseFont(),
              ))));
    } else {
      children.add(CupertinoFormRow(
          prefix: Padding(
              padding: const EdgeInsets.only(right: 20),
              child: Text(
                '音源状态',
                style: TextStyle(color: textColor).useSystemChineseFont(),
              )),
          child: Container(
              padding: const EdgeInsets.only(right: 10),
              alignment: Alignment.centerRight,
              height: 50,
              child: Text(
                "未导入",
                style: TextStyle(color: activeIconRed).useSystemChineseFont(),
              ))));
    }
    if (hasExternApi) {

    }
    if (hasExternApi) {
      // 添加更新检查按钮
      children.add(CupertinoFormRow(
          prefix: Padding(
              padding: const EdgeInsets.only(right: 20),
              child: Text(
                '检查更新',
                style: TextStyle(color: textColor).useSystemChineseFont(),
              )),
          child: CupertinoButton(
              onPressed: () async {
                await _checkMusicSourceUpdate(context);
              },
              child: Icon(
                CupertinoIcons.refresh,
                color: iconColor,
              ))));

      // 删除音源按钮
      children.add(CupertinoFormRow(
          prefix: Padding(
              padding: const EdgeInsets.only(right: 20),
              child: Text(
                '删除音源',
                style: TextStyle(color: textColor).useSystemChineseFont(),
              )),
          child: CupertinoButton(
              onPressed: () async {
                bool? confirmed = await showConfirmDialog(
                  context,
                  "确认删除",
                  "确定要删除当前音乐源吗？此操作不可撤销。",
                );
                if (confirmed == true) {
                  // 删除JavaScript音乐源配置
                  var jsApiConfigPath = '$globalDocumentPath/js_api_config.json';
                  var jsApiConfigFile = File(jsApiConfigPath);
                  if (await jsApiConfigFile.exists()) {
                    await jsApiConfigFile.delete();
                  }

                  // 删除本地音乐源文件
                  var jsSourcesDir = Directory('$globalDocumentPath/js_sources');
                  if (await jsSourcesDir.exists()) {
                    await jsSourcesDir.delete(recursive: true);
                  }

                  globalJsApiEvaler?.dispose();
                  globalJsApiEvaler = null;

                  setState(() {});
                  LogToast.success("删除成功", "音乐源已删除", "[More Page] Music source deleted successfully");
                }
              },
              child: Icon(
                CupertinoIcons.delete,
                color: activeIconRed,
              ))));
    } else {
      children.add(CupertinoFormRow(
          prefix: Padding(
              padding: const EdgeInsets.only(right: 20),
              child: Text(
                '导入音源',
                style: TextStyle(color: textColor).useSystemChineseFont(),
              )),
          child: ImportExternApiMenu(
              builder: (context, showMenu) => CupertinoButton(
                  onPressed: showMenu,
                  child:
                      Icon(CupertinoIcons.music_note_2, color: iconColor)))));
    }
    return CupertinoFormSection.insetGrouped(
        header: Text("自定义音源",
            style: TextStyle(color: textColor).useSystemChineseFont()),
        children: children);
  }
}

@immutable
class ImportExternApiMenu extends StatelessWidget {
  const ImportExternApiMenu({
    super.key,
    required this.builder,
  });
  final PullDownMenuButtonBuilder builder;

  @override
  Widget build(BuildContext context) {
    return PullDownButton(
      itemBuilder: (context) => [
        PullDownMenuItem(
          itemTheme: PullDownMenuItemTheme(
              textStyle: const TextStyle().useSystemChineseFont()),
          onTap: () async {
            FilePickerResult? result = await FilePicker.platform.pickFiles(
              type: FileType.custom,
              allowedExtensions: ['js', 'dart'],
            );

            if (result != null) {
              var filePath = result.files.single.path!;
              var fileName = result.files.single.name;

              try {
                if (fileName.endsWith('.js')) {
                  // JavaScript音乐源
                  var jsApi = await JsApi.fromPath(path: filePath);
                  await saveJsApiConfig(jsApi);
                  globalJsApiEvaler = JsApiEvaler(jsApi.localPath);
                  LogToast.success("导入成功", "JavaScript音乐源导入成功",
                      "[More Page] JavaScript music source imported successfully");
                } else {
                  LogToast.error("不支持的文件格式", "只支持JavaScript音乐源(.js文件)",
                      "[More Page] Unsupported file format, only JavaScript sources (.js) are supported");
                }

                if (context.mounted) {
                  context.findAncestorStateOfType<MorePageState>()?.refresh();
                }
              } catch (e) {
                globalTalker.error("[More Page] 导入第三方音乐源失败:$e");
                LogToast.error("导入失败", "导入第三方音乐源失败: $e",
                    "[More Page] Failed to import music source: $e");
              }
            }
          },
          title: '文件导入',
          icon: CupertinoIcons.folder,
        ),
        PullDownMenuItem(
          itemTheme: PullDownMenuItemTheme(
              textStyle: const TextStyle().useSystemChineseFont()),
          onTap: () async {
            await _showUrlImportDialog(context);
          },
          title: 'URL导入',
          icon: CupertinoIcons.link,
        ),

      ],
      animationBuilder: null,
      position: PullDownMenuPosition.automatic,
      buttonBuilder: builder,
    );
  }
}

Future<void> _showUrlImportDialog(BuildContext context) async {
  final TextEditingController urlController = TextEditingController();
  final Brightness brightness = MediaQuery.of(context).platformBrightness;
  final bool isDarkMode = brightness == Brightness.dark;

  return showCupertinoDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return CupertinoAlertDialog(
        title: Text(
          'URL导入音乐源',
          style: TextStyle(
            color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
          ).useSystemChineseFont(),
        ),
        content: Column(
          children: [
            const SizedBox(height: 10),
            Text(
              '请输入JavaScript音乐源的URL地址',
              style: TextStyle(
                color: isDarkMode ? CupertinoColors.systemGrey : CupertinoColors.systemGrey2,
              ).useSystemChineseFont(),
            ),
            const SizedBox(height: 10),
            CupertinoTextField(
              controller: urlController,
              placeholder: 'https://example.com/music_source.js',
              style: TextStyle(
                color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
              ).useSystemChineseFont(),
              placeholderStyle: TextStyle(
                color: isDarkMode ? CupertinoColors.systemGrey : CupertinoColors.systemGrey2,
              ).useSystemChineseFont(),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text(
              '取消',
              style: TextStyle(
                color: isDarkMode ? CupertinoColors.systemGrey2 : CupertinoColors.activeBlue,
              ).useSystemChineseFont(),
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          CupertinoDialogAction(
            child: Text(
              '导入',
              style: TextStyle(
                color: isDarkMode ? CupertinoColors.systemGrey2 : CupertinoColors.activeBlue,
              ).useSystemChineseFont(),
            ),
            onPressed: () async {
              Navigator.of(context).pop();
              await _importFromUrl(context, urlController.text.trim());
            },
          ),
        ],
      );
    },
  );
}

Future<void> _importFromUrl(BuildContext context, String url) async {
  if (url.isEmpty) {
    LogToast.error("URL为空", "请输入有效的URL地址", "[More Page] URL is empty");
    return;
  }

  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    LogToast.error("URL格式错误", "URL必须以http://或https://开头", "[More Page] Invalid URL format");
    return;
  }

  try {
    LogToast.info("正在导入", "正在从URL下载音乐源...", "[More Page] Downloading music source from URL");

    var jsApi = await JsApi.fromUrl(url: url);
    await saveJsApiConfig(jsApi);
    globalJsApiEvaler = JsApiEvaler(jsApi.localPath);

    LogToast.success("导入成功", "JavaScript音乐源导入成功", "[More Page] JavaScript music source imported successfully from URL");

    if (context.mounted) {
      context.findAncestorStateOfType<MorePageState>()?.refresh();
    }
  } catch (e) {
    globalTalker.error("[More Page] URL导入第三方音乐源失败:$e");
    LogToast.error("导入失败", "从URL导入音乐源失败: $e", "[More Page] Failed to import music source from URL: $e");
  }
}

Future<void> _checkMusicSourceUpdate(BuildContext context) async {
  try {
    // 读取当前音乐源配置
    var jsApiConfigPath = '$globalDocumentPath/js_api_config.json';
    var jsApiConfigFile = File(jsApiConfigPath);

    if (!await jsApiConfigFile.exists()) {
      LogToast.error("配置不存在", "未找到音乐源配置文件", "[More Page] Music source config not found");
      return;
    }

    var configContent = await jsApiConfigFile.readAsString();
    var configJson = jsonDecode(configContent);
    var currentUrl = configJson['url'] as String?;

    if (currentUrl == null || currentUrl.isEmpty) {
      LogToast.error("无法更新", "当前音乐源不是从URL导入的，无法检查更新", "[More Page] Current music source is not from URL");
      return;
    }

    LogToast.info("检查更新", "正在检查音乐源更新...", "[More Page] Checking music source update");

    // 下载最新版本
    var updatedJsApi = await JsApi.fromUrl(url: currentUrl);

    // 比较哈希值
    var currentHash = configJson['lastHash'] as String?;
    if (currentHash != null && currentHash == updatedJsApi.lastHash) {
      LogToast.info("已是最新", "音乐源已是最新版本", "[More Page] Music source is up to date");
      return;
    }

    // 更新配置
    await saveJsApiConfig(updatedJsApi);

    // 重新初始化
    globalJsApiEvaler?.dispose();
    globalJsApiEvaler = JsApiEvaler(updatedJsApi.localPath);

    LogToast.success("更新成功", "音乐源已更新到最新版本", "[More Page] Music source updated successfully");

    if (context.mounted) {
      context.findAncestorStateOfType<MorePageState>()?.refresh();
    }
  } catch (e) {
    globalTalker.error("[More Page] 检查音乐源更新失败:$e");
    LogToast.error("更新失败", "检查音乐源更新失败: $e", "[More Page] Failed to check music source update: $e");
  }
}

Future<bool?> showConfirmDialog(BuildContext context, String title, String content) async {
  final Brightness brightness = MediaQuery.of(context).platformBrightness;
  final bool isDarkMode = brightness == Brightness.dark;

  return showCupertinoDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return CupertinoAlertDialog(
        title: Text(
          title,
          style: TextStyle(
            color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
          ).useSystemChineseFont(),
        ),
        content: Text(
          content,
          style: TextStyle(
            color: isDarkMode ? CupertinoColors.systemGrey : CupertinoColors.systemGrey2,
          ).useSystemChineseFont(),
        ),
        actions: [
          CupertinoDialogAction(
            child: Text(
              '取消',
              style: TextStyle(
                color: isDarkMode ? CupertinoColors.systemGrey2 : CupertinoColors.activeBlue,
              ).useSystemChineseFont(),
            ),
            onPressed: () {
              Navigator.of(context).pop(false);
            },
          ),
          CupertinoDialogAction(
            child: Text(
              '确认',
              style: TextStyle(
                color: isDarkMode ? CupertinoColors.systemGrey2 : CupertinoColors.destructiveRed,
              ).useSystemChineseFont(),
            ),
            onPressed: () {
              Navigator.of(context).pop(true);
            },
          ),
        ],
      );
    },
  );
}

CupertinoFormSection _buildQualitySelectSection(
    BuildContext context, void Function() refresh, Color textColor) {
  List<Widget> children = [];

  // 统一的默认播放音质选择
  children.add(CupertinoFormRow(
      prefix: Text("默认播放音质",
          style: TextStyle(color: textColor).useSystemChineseFont()),
      child: CupertinoButton(
          onPressed: () async {
            QualityOption? selectedOption =
                await showQualityOptionDialog(context);
            if (selectedOption != null) {
              String qualityString = qualityOptionToString(selectedOption);
              // 同时设置WiFi和移动网络音质为相同值
              globalConfig.wifiAutoQuality = qualityString;
              globalConfig.mobileAutoQuality = qualityString;
              await globalConfig.save();
            }
            refresh();
          },
          child: Text(globalConfig.wifiAutoQuality,
              style: TextStyle(color: textColor).useSystemChineseFont()))));
  return CupertinoFormSection.insetGrouped(
    header:
        Text('音质选择', style: TextStyle(color: textColor).useSystemChineseFont()),
    children: children,
  );
}

CupertinoFormSection _buildExportCacheRoot(BuildContext context,
    void Function() refresh, Color textColor, Color iconColor) {
  Future<void> exportCacheRoot(bool copy) async {
    var path = await pickDirectory();
    if (path == null) return;
    if (globalConfig.exportCacheRoot != null &&
        globalConfig.exportCacheRoot == path) {
      LogToast.info("数据设定", "与原文件夹相同, 无需操作",
          "[exportCacheRoot] Same as original folder, no need to operate");
      return;
    }
    try {
      try {
        if (context.mounted) {
          await showWaitDialog(context, "正在处理中,稍后将自动退出应用以应用更改");
        }
        await globalAudioHandler.clear();
        await SqlFactoryW.shutdown();
        late String originRootPath;
        if (globalConfig.exportCacheRoot != null &&
            globalConfig.exportCacheRoot!.isNotEmpty) {
          originRootPath = globalConfig.exportCacheRoot!;
        } else {
          originRootPath = "$globalDocumentPath/AppRhyme";
        }
        if (copy) {
          await copyDirectory(
              src: "$originRootPath/$picCacheRoot", dst: "$path/$picCacheRoot");
          await copyDirectory(
              src: "$originRootPath/$musicCacheRoot",
              dst: "$path/$musicCacheRoot");
          await copyFile(
              from: "$originRootPath/MusicData.db", to: "$path/MusicData.db");
        }

        globalConfig.lastExportCacheRoot = globalConfig.exportCacheRoot;
        globalConfig.exportCacheRoot = path;
        globalConfig.save();
        if (context.mounted) {
          context.findAncestorStateOfType<MorePageState>()?.refresh();
        }
        await SqlFactoryW.initFromPath(filepath: "$path/MusicData.db");
      } finally {
        if (context.mounted) {
          Navigator.pop(context);
        }
      }
      try {
        if (context.mounted) {
          await showWaitDialog(context,
              "应用将在3秒后退出\n下次打开时将删除旧文件夹下数据, 并应用新文件夹下数据\n如未正常退出, 请关闭应用后重新打开");
        }
        await Future.delayed(const Duration(seconds: 3));
      } finally {
        if (context.mounted) {
          Navigator.pop(context);
        }
        await exitApp();
      }
    } catch (e) {
      LogToast.error("数据设定", "数据设定失败: $e", "[exportCacheRoot] $e");
    }
  }

  List<CupertinoFormRow> children = [];
  if (globalConfig.exportCacheRoot == null) {
    children.add(CupertinoFormRow(
        prefix: Padding(
            padding: const EdgeInsets.only(right: 20),
            child: Text(
              '当前数据状态',
              style: TextStyle(color: textColor).useSystemChineseFont(),
            )),
        child: Container(
            padding: const EdgeInsets.only(right: 10),
            alignment: Alignment.centerRight,
            height: 50,
            child: Text(
              "应用内部数据",
              style: TextStyle(color: textColor).useSystemChineseFont(),
            ))));
  } else {
    children.add(CupertinoFormRow(
        prefix: Padding(
            padding: const EdgeInsets.only(right: 20),
            child: Text(
              '当前数据文件夹',
              style: TextStyle(color: textColor).useSystemChineseFont(),
            )),
        child: Container(
            padding: const EdgeInsets.only(right: 10),
            alignment: Alignment.centerRight,
            height: 50,
            child: Text(
              globalConfig.exportCacheRoot!,
              style: TextStyle(color: textColor).useSystemChineseFont(),
            ))));
  }
  children.add(
    CupertinoFormRow(
      prefix: Text(
        '迁移数据文件夹',
        style: TextStyle(color: textColor).useSystemChineseFont(),
      ),
      child: CupertinoButton(
        onPressed: () async {
          var confirm = await showConfirmationDialog(
              context,
              "注意!\n"
              "迁移数据将会将当前使用文件夹下的数据迁移到新的文件夹下\n"
              "请确保新的文件夹下没有AppRhyme的数据, 否则会导致该文件夹中数据完全丢失!!!\n"
              "如果你想直接使用指定文件夹下的数据, 请使用'使用数据'功能\n"
              "操作后应用将会自动退出, 请重新打开应用以应用更改\n"
              "是否继续?");
          if (confirm != null && confirm) {
            await exportCacheRoot(true);
          }
        },
        child: Icon(CupertinoIcons.folder, color: iconColor),
      ),
    ),
  );
  children.add(
    CupertinoFormRow(
      prefix: Text(
        '使用数据文件夹',
        style: TextStyle(color: textColor).useSystemChineseFont(),
      ),
      child: CupertinoButton(
        onPressed: () async {
          var confirm = await showConfirmationDialog(
              context,
              "注意!\n"
              "使用数据将会直接使用指定文件夹下的数据, 请确保指定下有正确的数据\n"
              "这将会导致当前使用的文件夹下的数据完全丢失!!!\n"
              "如果你想迁移数据, 请使用'迁移数据'功能\n"
              "操作后应用将会自动退出, 请重新打开应用以应用更改\n"
              "是否继续?");
          if (confirm != null && confirm) {
            await exportCacheRoot(false);
          }
        },
        child: Icon(CupertinoIcons.folder, color: iconColor),
      ),
    ),
  );
  return CupertinoFormSection.insetGrouped(
    header:
        Text('数据设定', style: TextStyle(color: textColor).useSystemChineseFont()),
    children: children,
  );
}
