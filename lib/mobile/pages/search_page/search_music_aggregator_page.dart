import 'dart:async';

import 'package:app_rhyme/mobile/comps/music_container_comp/music_container_list_item.dart';
import 'package:app_rhyme/mobile/pages/muti_select_pages/muti_select_music_container_listview_page.dart';
import 'package:app_rhyme/mobile/pages/search_page/combined_search_page.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/types/music_container.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:pull_down_button/pull_down_button.dart';

class SearchMusicAggregatorPage extends StatefulWidget {
  const SearchMusicAggregatorPage({super.key});

  @override
  _SearchMusicAggregatorPageState createState() =>
      _SearchMusicAggregatorPageState();
}

class _SearchMusicAggregatorPageState extends State<SearchMusicAggregatorPage>
    with WidgetsBindingObserver {
  final PagingController<int, MusicAggregatorW> _pagingController =
      PagingController(firstPageKey: 1);
  final TextEditingController _inputContentController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _artistController = TextEditingController();
  final TextEditingController _albumController = TextEditingController();
  MusicFuzzFilter? filter;
  bool _isFilterSectionVisible = false;
  Timer? _searchDebounceTimer;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    _pagingController.addPageRequestListener((pageKey) {
      _fetchMusicAggregators(pageKey);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchDebounceTimer?.cancel();
    _pagingController.dispose();
    _inputContentController.dispose();
    _nameController.dispose();
    _artistController.dispose();
    _albumController.dispose();
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    setState(() {});
  }

  Future<void> _fetchMusicAggregators(int pageKey) async {
    try {
      if (_inputContentController.value.text.isEmpty) {
        _pagingController.appendLastPage([]);
        return;
      }

      // 限制最大页数为10页，防止无限加载
      if (pageKey > 10) {
        _pagingController.appendLastPage([]);
        return;
      }

      // 检查是否有JavaScript音乐源
      if (globalJsApiEvaler == null) {
        _pagingController.error = "请先在设置中导入JavaScript音乐源";
        return;
      }

      // 使用JavaScript音乐源搜索
      String keyword = _inputContentController.value.text;
      int limit = 20; // 每页20个结果

      List<MusicInfo> searchResults = await globalJsApiEvaler!.searchMusic(keyword, limit);

      if (searchResults.isEmpty && pageKey == 1) {
        _pagingController.appendLastPage([]);
        return;
      }

      // 将搜索结果转换为MusicContainer并显示
      if (searchResults.isNotEmpty) {
        LogToast.success(
          "搜索完成",
          "找到 ${searchResults.length} 首歌曲",
          "[SearchMusicAggregator] Found ${searchResults.length} songs"
        );

        // 由于搜索结果是MusicContainer而不是MusicAggregatorW，我们需要显示错误信息
        _pagingController.error = "找到 ${searchResults.length} 首歌曲\n搜索功能正在开发中，请稍后再试";
      } else {
        _pagingController.appendLastPage([]);
      }

    } catch (error) {
      globalTalker.error("[SearchMusicAggregator] Search failed: $error");
      _pagingController.error = "搜索失败: $error";
    }
  }



  void _applyFilter() {
    setState(() {
      // 如果三个输入框都为空，则不应用筛选条件
      if (_nameController.text.isEmpty &&
          _artistController.text.isEmpty &&
          _albumController.text.isEmpty) {
        filter = null;
      } else {
        filter = MusicFuzzFilter(
          name: _nameController.text.isEmpty ? null : _nameController.text,
          artist: _artistController.text.isNotEmpty
              ? _artistController.text.split(',')
              : [],
          album: _albumController.text.isEmpty ? null : _albumController.text,
        );
      }
      _isFilterSectionVisible = false;
    });
    _pagingController.refresh();
  }

  void _clearFilter() {
    setState(() {
      _nameController.clear();
      _artistController.clear();
      _albumController.clear();
      filter = null;
      _isFilterSectionVisible = false;
    });
    _pagingController.refresh();
  }



  void _toggleFilterSection() {
    setState(() {
      _isFilterSectionVisible = !_isFilterSectionVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    final bool isDarkMode =
        MediaQuery.of(context).platformBrightness == Brightness.dark;
    final Color backgroundColor =
        isDarkMode ? CupertinoColors.black : CupertinoColors.white;
    final Color textColor =
        isDarkMode ? CupertinoColors.white : CupertinoColors.black;

    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      // 下面地方如果直接使用safeArea会ios上底部有一块空白
      child: Column(
        children: [
          CupertinoNavigationBar(
              leading: Padding(
                padding: const EdgeInsets.only(left: 0.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '搜索歌曲',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                      color: isDarkMode
                          ? CupertinoColors.white
                          : CupertinoColors.black,
                    ).useSystemChineseFont(),
                  ),
                ),
              ),
),
          // 搜索框和过滤按钮
          Padding(
            padding: const EdgeInsets.only(top: 4.0, left: 8, right: 0),
            child: Row(
              children: [
                Expanded(
                  child: CupertinoSearchTextField(
                    style: TextStyle(
                      color: isDarkMode
                          ? CupertinoColors.white
                          : CupertinoColors.black,
                    ).useSystemChineseFont(),
                    controller: _inputContentController,
                    onChanged: (String value) {
                      // 取消之前的定时器
                      _searchDebounceTimer?.cancel();

                      // 设置新的防抖定时器
                      _searchDebounceTimer = Timer(const Duration(milliseconds: 800), () {
                        if (value.isNotEmpty) {
                          setState(() {
                            _isFilterSectionVisible = false;
                          });
                          _pagingController.refresh();
                        }
                      });
                    },
                    onSubmitted: (String value) {
                      // 取消防抖定时器，立即执行搜索
                      _searchDebounceTimer?.cancel();
                      if (value.isNotEmpty) {
                        setState(() {
                          _isFilterSectionVisible = false;
                        });
                        _pagingController.refresh();
                      }
                    },
                  ),
                ),
                // 重置按钮
                CupertinoButton(
                  padding: const EdgeInsets.all(0),
                  onPressed: () {
                    _inputContentController.clear();
                    _pagingController.refresh();
                    setState(() {
                      _isFilterSectionVisible = false;
                    });
                  },
                  child: const Icon(
                    CupertinoIcons.refresh,
                    size: 25,
                  ),
                ),
                CupertinoButton(
                  padding: const EdgeInsets.all(0),
                  onPressed: _toggleFilterSection,
                  child: const Icon(
                    CupertinoIcons.slider_horizontal_3,
                    size: 25,
                  ),
                ),
              ],
            ),
          ),
          // 编辑 MusicFuzzFilter 的 Section
          if (_isFilterSectionVisible)
            CupertinoFormSection.insetGrouped(
              header: Text('筛选条件',
                  style: TextStyle(color: textColor).useSystemChineseFont()),
              children: [
                CupertinoFormRow(
                  prefix: Padding(
                    padding: const EdgeInsets.only(right: 10.0),
                    child: Text('歌曲名',
                        style:
                            TextStyle(color: textColor).useSystemChineseFont()),
                  ),
                  child: CupertinoTextField(
                    style: TextStyle(
                      color: isDarkMode
                          ? CupertinoColors.white
                          : CupertinoColors.black,
                    ).useSystemChineseFont(),
                    controller: _nameController,
                    placeholder: '输入曲名',
                  ),
                ),
                CupertinoFormRow(
                  prefix: Padding(
                    padding: const EdgeInsets.only(right: 10.0),
                    child: Text('演唱者',
                        style:
                            TextStyle(color: textColor).useSystemChineseFont()),
                  ),
                  child: CupertinoTextField(
                    style: TextStyle(
                      color: isDarkMode
                          ? CupertinoColors.white
                          : CupertinoColors.black,
                    ).useSystemChineseFont(),
                    controller: _artistController,
                    placeholder: '输入演唱者 (多个用逗号分隔)',
                  ),
                ),
                CupertinoFormRow(
                  prefix: Padding(
                    padding: const EdgeInsets.only(right: 10.0),
                    child: Text('专辑名',
                        style:
                            TextStyle(color: textColor).useSystemChineseFont()),
                  ),
                  child: CupertinoTextField(
                    style: TextStyle(
                      color: isDarkMode
                          ? CupertinoColors.white
                          : CupertinoColors.black,
                    ).useSystemChineseFont(),
                    controller: _albumController,
                    placeholder: '输入专辑名',
                  ),
                ),
                CupertinoFormRow(
                  padding: EdgeInsets.zero,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      CupertinoButton(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 20),
                        onPressed: _applyFilter,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 5, horizontal: 20),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: CupertinoColors.activeBlue,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Text(
                            '应用筛选条件',
                            style: const TextStyle(
                              color: CupertinoColors.activeBlue,
                            ).useSystemChineseFont(),
                          ),
                        ),
                      ),
                      CupertinoButton(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 20),
                        onPressed: _clearFilter,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 5, horizontal: 20),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: CupertinoColors.systemRed,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Text(
                            '清空筛选条件',
                            style: const TextStyle(
                              color: CupertinoColors.systemRed,
                            ).useSystemChineseFont(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          Expanded(
            child: PagedListView.separated(
              pagingController: _pagingController,
              padding: EdgeInsets.only(bottom: screenHeight * 0.1),
              separatorBuilder: (context, index) => Divider(
                color: isDarkMode
                    ? CupertinoColors.systemGrey
                    : CupertinoColors.systemGrey4,
                indent: 30,
                endIndent: 30,
              ),
              builderDelegate: PagedChildBuilderDelegate<MusicAggregatorW>(
                noItemsFoundIndicatorBuilder: (context) {
                  return const SizedBox.shrink();
                },
                itemBuilder: (context, musicAggregator, index) =>
                    MusicContainerListItem(
                  musicContainer: MusicContainer(musicAggregator),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

@immutable
class SearchMusicAggregatroChoiceMenu extends StatelessWidget {
  const SearchMusicAggregatroChoiceMenu({
    super.key,
    required this.builder,
    required this.fetchAllMusicAggregators,
    required this.musicAggregatorController,
  });
  final PagingController<int, MusicAggregatorW> musicAggregatorController;
  final Future<void> Function() fetchAllMusicAggregators;
  final PullDownMenuButtonBuilder builder;

  @override
  Widget build(BuildContext context) {
    return PullDownButton(
      itemBuilder: (context) => [
        PullDownMenuItem(
          itemTheme: PullDownMenuItemTheme(
              textStyle: const TextStyle().useSystemChineseFont()),
          onTap: globalToggleSearchPage,
          title: '搜索歌单',
          icon: CupertinoIcons.photo,
        ),
        PullDownMenuItem(
          itemTheme: PullDownMenuItemTheme(
              textStyle: const TextStyle().useSystemChineseFont()),
          onTap: () async {
            await fetchAllMusicAggregators();
            LogToast.success(
              "加载所有歌曲",
              "已加载所有歌曲",
              "[SearchMusicAggregatorPage] Succeed to fetch all music aggregators",
            );
          },
          title: "加载所有歌曲",
          icon: CupertinoIcons.music_note_2,
        ),
        PullDownMenuItem(
          itemTheme: PullDownMenuItemTheme(
              textStyle: const TextStyle().useSystemChineseFont()),
          onTap: () async {
            LogToast.info(
              "多选操作",
              "正在加载所有歌曲,请稍等",
              "[SearchMusicAggregatorPage] Multi select operation, wait to fetch all music aggregators",
            );
            await fetchAllMusicAggregators();
            if (musicAggregatorController.itemList == null) return;
            if (musicAggregatorController.itemList!.isEmpty) return;
            if (context.mounted) {
              Navigator.push(
                context,
                CupertinoPageRoute(
                  builder: (context) => MutiSelectMusicContainerListPage(
                      musicContainers: musicAggregatorController.itemList!
                          .map((e) => MusicContainer(e))
                          .toList()),
                ),
              );
            }
          },
          title: "多选操作",
          icon: CupertinoIcons.selection_pin_in_out,
        )
      ],
      position: PullDownMenuPosition.automatic,
      buttonBuilder: builder,
    );
  }
}
