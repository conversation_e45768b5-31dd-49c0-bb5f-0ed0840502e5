import 'package:flutter/cupertino.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:chinese_font_library/chinese_font_library.dart';

// 定义一个枚举来表示当前的页面状态
enum PageState { home, lyric, list }

class BottomButton extends StatefulWidget {
  final VoidCallback onList;
  final VoidCallback onLyric;
  final VoidCallback? onTranslation;
  final EdgeInsetsGeometry padding;
  final double size;
  const BottomButton(
      {super.key,
      required this.onList,
      required this.onLyric,
      this.onTranslation,
      required this.padding,
      required this.size});

  @override
  State<StatefulWidget> createState() => BottomButtonState();
}

class BottomButtonState extends State<BottomButton> {
  // 使用PageState枚举来跟踪当前的页面状态
  PageState currentPage = PageState.home;
  bool showTranslation = false;

  @override
  Widget build(BuildContext context) {
    // 检查当前播放的音乐是否有翻译歌词
    final currentMusic = globalAudioHandler.playingMusic.value;
    final hasTranslation = currentMusic?.info.tlyric != null &&
                          currentMusic!.info.tlyric!.isNotEmpty;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: <Widget>[
        CupertinoButton(
          padding: widget.padding,
          child: Icon(
            currentPage == PageState.lyric
                ? CupertinoIcons.quote_bubble_fill
                : CupertinoIcons.quote_bubble,
            color: CupertinoColors.white,
            size: widget.size,
          ),
          onPressed: () {
            setState(() {
              currentPage = currentPage == PageState.lyric
                  ? PageState.home
                  : PageState.lyric;
            });
            widget.onLyric();
          },
        ),
        // 翻译按钮（仅在有翻译歌词时显示）
        if (hasTranslation && widget.onTranslation != null)
          CupertinoButton(
            padding: widget.padding,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: showTranslation
                    ? CupertinoColors.activeBlue.withOpacity(0.3)
                    : CupertinoColors.systemGrey6.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: showTranslation
                      ? CupertinoColors.activeBlue
                      : CupertinoColors.systemGrey4,
                  width: 1,
                ),
              ),
              child: Text(
                showTranslation ? '译' : '原',
                style: TextStyle(
                  color: showTranslation
                      ? CupertinoColors.activeBlue
                      : CupertinoColors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ).useSystemChineseFont(),
              ),
            ),
            onPressed: () {
              setState(() {
                showTranslation = !showTranslation;
              });
              widget.onTranslation!();
            },
          ),
        CupertinoButton(
          padding: widget.padding,
          child: Icon(
            // 根据当前页面状态来决定图标
            currentPage == PageState.list
                ? CupertinoIcons.square_list_fill
                : CupertinoIcons.square_list,
            color: CupertinoColors.white,
            size: widget.size,
          ),
          onPressed: () {
            setState(() {
              // 如果当前在歌单页面，点击则回到主页
              // 否则，进入歌单页面
              currentPage = currentPage == PageState.list
                  ? PageState.home
                  : PageState.list;
            });
            widget.onList();
          },
        ),
      ],
    );
  }
}
