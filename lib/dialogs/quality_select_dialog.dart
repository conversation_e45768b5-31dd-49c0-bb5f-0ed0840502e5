import 'package:app_rhyme/utils/quality_picker.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flutter/cupertino.dart';

Future<QualityOption?> showQualityOptionDialog(BuildContext context) async {
  final Brightness brightness = MediaQuery.of(context).platformBrightness;
  final bool isDarkMode = brightness == Brightness.dark;

  // 尝试从JavaScript音乐源获取可用音质选项
  List<Map<String, dynamic>> availableQualities = [];
  if (globalJsApiEvaler != null) {
    try {
      availableQualities = await globalJsApiEvaler!.getAvailableQualities();
    } catch (e) {
      // 如果获取失败，使用默认选项
      availableQualities = [
        {'summary': '标准', 'bitrate': 128, 'format': 'mp3'},
        {'summary': '高品质', 'bitrate': 320, 'format': 'mp3'},
      ];
    }
  } else {
    // 没有JavaScript音乐源时使用默认选项
    availableQualities = [
      {'summary': '标准', 'bitrate': 128, 'format': 'mp3'},
      {'summary': '高品质', 'bitrate': 320, 'format': 'mp3'},
    ];
  }

  if (!context.mounted) return null;

  return await showCupertinoModalPopup<QualityOption>(
    context: context,
    builder: (BuildContext context) {
      return CupertinoActionSheet(
        title: Text(
          '选择音质选项',
          style: TextStyle(
            color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
          ).useSystemChineseFont(),
        ),
        actions: availableQualities.map<Widget>((quality) {
          String qualityName = quality['summary'] ?? '未知';
          int bitrate = quality['bitrate'] ?? 128;
          String format = quality['format'] ?? 'mp3';

          // 根据音质名称映射到QualityOption
          QualityOption option = qualityName.contains('高')
              ? QualityOption.highest
              : QualityOption.medium;

          return CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context, option);
            },
            child: Text(
              '$qualityName ($bitrate kbps $format)',
              style: TextStyle(
                color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
              ).useSystemChineseFont(),
            ),
          );
        }).toList(),
        cancelButton: CupertinoActionSheetAction(
          onPressed: () {
            Navigator.pop(context, null); // null when cancelled
          },
          child: Text(
            '取消',
            style: TextStyle(
              color: isDarkMode
                  ? CupertinoColors.systemGrey2
                  : CupertinoColors.activeBlue,
            ).useSystemChineseFont(),
          ),
        ),
      );
    },
  );
}
