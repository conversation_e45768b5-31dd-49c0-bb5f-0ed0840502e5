import 'package:app_rhyme/src/rust/api/types/version.dart';

import 'package:app_rhyme/utils/log_toast.dart';
import 'package:app_rhyme/dialogs/extern_api_update_dialog.dart';
import 'package:app_rhyme/dialogs/version_update_dialog.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:flutter/widgets.dart';

Future<void> checkVersionUpdate(BuildContext context, bool toast) async {
  // 禁用版本更新检查功能
  if (toast) {
    LogToast.info("版本更新", "更新检查功能已禁用",
        "[checkVersionUpdate] Version update check is disabled");
  }
  return;

  // 原有的更新检查代码已被禁用
  // try {
  //   if (toast) {
  //     LogToast.info("检查应用版本更新", "正在加载数据,请稍等",
  //         "[checkVersionUpdate] Checking app version update");
  //   }

  //   var release = await checkUpdate(currentVersion: globalPackageInfo.version);
  //   if (context.mounted && release != null) {
  //     showVersionUpdateDialog(context, release);
  //   } else if (release == null) {
  //     if (toast) {
  //       LogToast.info("版本更新", "当前版本无需更新",
  //           "[checkVersionUpdate] Current version does not need to be updated");
  //     }
  //   }
  // } catch (e) {
  //   globalTalker.log("[VersionUpdate] $e");
  // }
}

Future<void> checkExternApiUpdate(BuildContext context, bool toast) async {
  // JavaScript音乐源暂不支持在线更新功能
  if (toast) {
    LogToast.info("音乐源更新", "JavaScript音乐源暂不支持在线更新功能",
        "[checkExternApiUpdate] JavaScript music sources do not support online updates yet");
  }
}

Future<void> autoCheckUpdate(BuildContext context) async {
  // 禁用自动更新检查功能
  // if (globalConfig.versionAutoUpdate) {
  //   await checkVersionUpdate(context, false);
  // }
  // if (globalConfig.externApiAutoUpdate) {
  //   if (context.mounted) {
  //     await checkExternApiUpdate(context, false);
  //   }
  // }
}
