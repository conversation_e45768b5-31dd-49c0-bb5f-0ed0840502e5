import 'dart:io';
import 'dart:convert';
import 'package:app_rhyme/audioControl/audio_controller.dart';
import 'package:app_rhyme/src/rust/api/init.dart';
import 'package:app_rhyme/src/rust/api/types/config.dart';
import 'package:app_rhyme/src/rust/api/bind/factory_bind.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/types/chore.dart';
import 'package:app_rhyme/types/extern_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:talker/talker.dart';

Talker globalTalker = Talker();

// 在`init_backend.dart` 中被初始化
late Config globalConfig;

late JsApiEvaler? globalJsApiEvaler;
late AudioHandler globalAudioHandler;
late AudioUiController globalAudioUiController;
late PackageInfo globalPackageInfo;
late String globalDocumentPath;
late Connectivity globalConnectivity;
ConnectivityStateSimple globalConnectivityStateSimple =
    ConnectivityStateSimple.none;

// 初始化全局变量
// 即可用于在App启动时也可用于配置更新时
Future<void> initGlobalVars() async {
  // 初始化rust全局配置，将documentPath设置为应用程序文档目录
  globalDocumentPath = (await getApplicationDocumentsDirectory()).path;
  // 初始化全局变量globalConfig
  globalConfig = await initBackend(storeRoot: globalDocumentPath);


  // 初始化全局JavaScript音乐源
  globalJsApiEvaler = null;
  try {
    var jsApiConfigPath = '$globalDocumentPath/js_api_config.json';
    var jsApiConfigFile = File(jsApiConfigPath);
    if (await jsApiConfigFile.exists()) {
      var jsApiConfigContent = await jsApiConfigFile.readAsString();
      var jsApiConfigJson = jsonDecode(jsApiConfigContent);
      var jsApiPath = jsApiConfigJson['localPath'] as String?;
      if (jsApiPath != null && await File(jsApiPath).exists()) {
        try {
          globalJsApiEvaler = JsApiEvaler(jsApiPath);
          globalTalker.info("[initGlobalVars] JavaScript API loaded successfully");
        } catch (jsError) {
          globalTalker.error("[initGlobalVars] Failed to initialize JavaScript API: $jsError");
          globalJsApiEvaler = null;
        }
      } else {
        globalTalker.info("[initGlobalVars] JavaScript API file not found, skipping");
      }
    } else {
      globalTalker.info("[initGlobalVars] No JavaScript API config found, skipping");
    }
  } catch (e) {
    globalTalker.error("[initGlobalVars] Failed to load JavaScript API config: $e");
    globalJsApiEvaler = null;
  }
  // 确保"我的收藏"歌单存在
  await ensureFavoritesPlaylistExists();

  // 初始化应用包信息
  globalPackageInfo = await PackageInfo.fromPlatform();
  // 监听网络状态变化
  globalConnectivity = Connectivity();
  globalConnectivity.onConnectivityChanged
      .listen((List<ConnectivityResult> connectivityResult) {
    if (connectivityResult.contains(ConnectivityResult.wifi)) {
      globalConnectivityStateSimple = ConnectivityStateSimple.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
      globalConnectivityStateSimple = ConnectivityStateSimple.mobile;
    } else {
      globalConnectivityStateSimple = ConnectivityStateSimple.none;
    }
  });
}

// 保存JavaScript音乐源配置
Future<void> saveJsApiConfig(JsApi jsApi) async {
  try {
    var jsApiConfigPath = '$globalDocumentPath/js_api_config.json';
    var jsApiConfigFile = File(jsApiConfigPath);
    var configJson = {
      'url': jsApi.url,
      'localPath': jsApi.localPath,
      'lastHash': jsApi.lastHash,
      'lastModifiedTime': jsApi.lastModifiedTime?.toIso8601String(),
    };
    await jsApiConfigFile.writeAsString(jsonEncode(configJson));
    globalTalker.info("[saveJsApiConfig] JavaScript API config saved");
  } catch (e) {
    globalTalker.error("[saveJsApiConfig] Failed to save JavaScript API config: $e");
  }
}

// 清除JavaScript音乐源配置
Future<void> clearJsApiConfig() async {
  try {
    var jsApiConfigPath = '$globalDocumentPath/js_api_config.json';
    var jsApiConfigFile = File(jsApiConfigPath);
    if (await jsApiConfigFile.exists()) {
      await jsApiConfigFile.delete();
    }
    globalJsApiEvaler?.dispose();
    globalJsApiEvaler = null;
    globalTalker.info("[clearJsApiConfig] JavaScript API config cleared");
  } catch (e) {
    globalTalker.error("[clearJsApiConfig] Failed to clear JavaScript API config: $e");
  }
}

// 确保"我的收藏"歌单存在
Future<void> ensureFavoritesPlaylistExists() async {
  try {
    // 获取所有歌单
    List<MusicListW> allPlaylists = await SqlFactoryW.getAllMusiclists();

    // 检查是否已存在"我的收藏"歌单
    bool favoritesExists = allPlaylists.any((playlist) =>
        playlist.getMusiclistInfo().name == "我的收藏");

    if (!favoritesExists) {
      // 创建"我的收藏"歌单
      MusicListInfo favoritesInfo = const MusicListInfo(
        id: 0, // 数据库会自动分配ID
        name: "我的收藏",
        artPic: "", // 空的封面图片
        desc: "收藏的音乐",
      );

      await SqlFactoryW.createMusiclist(musicListInfos: [favoritesInfo]);
      globalTalker.info("[ensureFavoritesPlaylistExists] Created '我的收藏' playlist");
    } else {
      globalTalker.info("[ensureFavoritesPlaylistExists] '我的收藏' playlist already exists");
    }
  } catch (e) {
    globalTalker.error("[ensureFavoritesPlaylistExists] Failed to ensure favorites playlist exists: $e");
  }
}
