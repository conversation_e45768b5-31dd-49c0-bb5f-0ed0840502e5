// 音乐源辅助函数

String sourceToShort(String source) {
  switch (source) {
    case 'netease':
      return '网易';
    case 'kuwo':
      return '酷我';
    case 'js':
      return 'JS';
    default:
      return source.length > 4 ? source.substring(0, 4) : source;
  }
}

String sourceToName(String source) {
  switch (source) {
    case 'netease':
      return '网易云音乐';
    case 'kuwo':
      return '酷我音乐';
    case 'js':
      return 'JavaScript音乐源';
    default:
      return source;
  }
}
