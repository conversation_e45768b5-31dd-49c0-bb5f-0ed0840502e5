import 'package:app_rhyme/utils/chore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

Future<void> initMobileDevice(BuildContext context) async {
  if (isDesktop()) {
    return;
  }

  // 获取当前主题模式
  final Brightness brightness = MediaQuery.of(context).platformBrightness;
  final bool isDarkMode = brightness == Brightness.dark;

  // 设置沉浸式状态栏和导航栏
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    systemNavigationBarColor: Colors.transparent,
    statusBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
    systemNavigationBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
    // 启用边到边显示
    systemNavigationBarDividerColor: Colors.transparent,
  ));

  // 启用沉浸式模式
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
  );

  if (!isTablet(context)) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }
}

// 动态更新系统UI样式以匹配当前主题
void updateSystemUIForTheme(bool isDarkMode) {
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    systemNavigationBarColor: Colors.transparent,
    statusBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
    systemNavigationBarIconBrightness: isDarkMode ? Brightness.light : Brightness.dark,
    systemNavigationBarDividerColor: Colors.transparent,
  ));
}
