import 'dart:io';
import 'dart:convert';
import 'package:crypto/crypto.dart' as crypto_lib;

import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/utils/global_vars.dart';

import 'package:flutter_js/flutter_js.dart';
import 'package:http/http.dart' as http;

class JsApiEvaler {
  late JavascriptRuntime jsRuntime;
  String jsCode = '';

  JsApiEvaler(String path) {
    try {
      jsRuntime = getJavascriptRuntime();
      var file = File(path);
      jsCode = file.readAsStringSync();

      // 注入HTTP请求函数和其他必要的全局对象
      _setupJavaScriptEnvironment();

      // 执行JavaScript代码
      try {
        jsRuntime.evaluate(jsCode);
        globalTalker.info("[JsApiEvaler] JavaScript music source loaded successfully");
      } catch (e) {
        globalTalker.error("[JsApiEvaler] Failed to execute JavaScript code: $e");
        rethrow;
      }
    } catch (e) {
      globalTalker.error("[JsApiEvaler] Failed to initialize JavaScript runtime: $e");
      rethrow;
    }
  }

  void _setupJavaScriptEnvironment() {
    // 注入fetch函数和其他必要的API
    jsRuntime.evaluate('''
      // 全局变量存储HTTP响应
      var _httpResponses = {};
      var _requestId = 0;

      // HTTP请求函数 - 这个函数会被Flutter端拦截并处理
      function httpRequest(url, options) {
        options = options || {};
        var method = options.method || 'GET';
        var headers = options.headers || {};
        var body = options.body || '';

        // 返回一个特殊的标记，Flutter端会识别并处理
        return '__FLUTTER_HTTP_REQUEST__' + JSON.stringify({
          url: url,
          method: method,
          headers: headers,
          body: body
        });
      }

      // 简化的fetch API - 返回Promise
      function fetch(url, options) {
        var requestResult = httpRequest(url, options);

        // 如果是Flutter HTTP请求标记，返回Promise
        if (typeof requestResult === 'string' && requestResult.startsWith('__FLUTTER_HTTP_REQUEST__')) {
          return Promise.resolve({
            ok: true,
            status: 200,
            statusText: 'OK',
            json: function() {
              // 这里会被Flutter端替换为真实的响应数据
              return Promise.resolve({ code: 200, data: [] });
            },
            text: function() {
              return Promise.resolve(JSON.stringify({ code: 200, data: [] }));
            }
          });
        }

        return Promise.resolve(requestResult);
      }

      // Promise polyfill for basic usage
      if (typeof Promise === 'undefined') {
        function Promise(executor) {
          var self = this;
          self.state = 'pending';
          self.value = undefined;
          self.handlers = [];

          function resolve(result) {
            if (self.state === 'pending') {
              self.state = 'fulfilled';
              self.value = result;
              self.handlers.forEach(handle);
              self.handlers = null;
            }
          }

          function reject(error) {
            if (self.state === 'pending') {
              self.state = 'rejected';
              self.value = error;
              self.handlers.forEach(handle);
              self.handlers = null;
            }
          }

          function handle(handler) {
            if (self.state === 'pending') {
              self.handlers.push(handler);
            } else {
              if (self.state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                handler.onFulfilled(self.value);
              }
              if (self.state === 'rejected' && typeof handler.onRejected === 'function') {
                handler.onRejected(self.value);
              }
            }
          }

          this.then = function(onFulfilled, onRejected) {
            return new Promise(function(resolve, reject) {
              handle({
                onFulfilled: function(result) {
                  try {
                    resolve(onFulfilled ? onFulfilled(result) : result);
                  } catch (ex) {
                    reject(ex);
                  }
                },
                onRejected: function(error) {
                  try {
                    resolve(onRejected ? onRejected(error) : error);
                  } catch (ex) {
                    reject(ex);
                  }
                }
              });
            });
          };

          try {
            executor(resolve, reject);
          } catch (ex) {
            reject(ex);
          }
        }

        Promise.resolve = function(value) {
          return new Promise(function(resolve) {
            resolve(value);
          });
        };

        Promise.reject = function(error) {
          return new Promise(function(resolve, reject) {
            reject(error);
          });
        };
      }

      // 全局对象
      var console = {
        log: function() { /* 日志输出 */ },
        error: function() { /* 错误输出 */ },
        warn: function() { /* 警告输出 */ },
        info: function() { /* 信息输出 */ }
      };
    ''');
  }

  // 处理HTTP请求
  Future<Map<String, dynamic>?> _handleHttpRequest(String url, String method, Map<String, String> headers, String body) async {
    try {
      globalTalker.info("[JsApiEvaler] Making HTTP request to: $url");

      http.Response response;
      var uri = Uri.parse(url);

      // 设置默认headers
      var requestHeaders = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        ...headers,
      };

      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: requestHeaders);
          break;
        case 'POST':
          response = await http.post(uri, headers: requestHeaders, body: body);
          break;
        case 'PUT':
          response = await http.put(uri, headers: requestHeaders, body: body);
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: requestHeaders);
          break;
        default:
          response = await http.get(uri, headers: requestHeaders);
      }

      if (response.statusCode == 200) {
        var jsonData = jsonDecode(response.body);
        globalTalker.info("[JsApiEvaler] HTTP request successful: $url");
        return jsonData;
      } else {
        globalTalker.error("[JsApiEvaler] HTTP request failed: ${response.statusCode} ${response.reasonPhrase}");
        return null;
      }
    } catch (e) {
      globalTalker.error("[JsApiEvaler] HTTP request error: $e");
      return null;
    }
  }

  Future<PlayInfo?> getMusicPlayInfo(String source, String extra) async {
    try {
      // 调用JavaScript函数
      var result = jsRuntime.evaluate('''
        (function() {
          try {
            if (typeof getMusicPlayInfo === 'function') {
              var result = getMusicPlayInfo('${source.replaceAll("'", "\\'")}', '${extra.replaceAll("'", "\\'")}');
              if (result && typeof result === 'object') {
                return JSON.stringify(result);
              } else if (typeof result === 'string') {
                return result;
              } else {
                return 'FUNCTION_RETURNED_NULL';
              }
            } else {
              return 'FUNCTION_NOT_FOUND';
            }
          } catch (e) {
            return 'ERROR: ' + e.toString();
          }
        })()
      ''');

      // 检查是否包含HTTP请求
      if (result.stringResult.contains('__FLUTTER_HTTP_REQUEST__')) {
        // 处理HTTP请求
        var httpRequestData = result.stringResult.replaceFirst('__FLUTTER_HTTP_REQUEST__', '');
        var requestInfo = jsonDecode(httpRequestData);

        var httpResponse = await _handleHttpRequest(
          requestInfo['url'],
          requestInfo['method'],
          Map<String, String>.from(requestInfo['headers']),
          requestInfo['body']
        );

        if (httpResponse != null) {
          // 将HTTP响应注入到JavaScript环境中，然后重新执行
          jsRuntime.evaluate('''
            var _lastHttpResponse = ${jsonEncode(httpResponse)};

            // 重写httpRequest函数返回真实数据
            function httpRequest(url, options) {
              return {
                ok: true,
                status: 200,
                statusText: 'OK',
                json: function() { return _lastHttpResponse; },
                text: function() { return JSON.stringify(_lastHttpResponse); }
              };
            }
          ''');

          // 重新调用JavaScript函数
          result = jsRuntime.evaluate('''
            (function() {
              try {
                if (typeof getMusicPlayInfo === 'function') {
                  var result = getMusicPlayInfo('${source.replaceAll("'", "\\'")}', '${extra.replaceAll("'", "\\'")}');
                  if (result && typeof result === 'object') {
                    return JSON.stringify(result);
                  } else if (typeof result === 'string') {
                    return result;
                  } else {
                    return 'FUNCTION_RETURNED_NULL';
                  }
                } else {
                  return 'FUNCTION_NOT_FOUND';
                }
              } catch (e) {
                return 'ERROR: ' + e.toString();
              }
            })()
          ''');
        }
      }

      if (result.stringResult == 'PROMISE_RESULT') {
        // 对于Promise结果，我们需要使用轮询或回调机制
        // 这里简化处理，直接返回null，后续可以改进
        globalTalker.warning("[JsApiEvaler] Promise result not fully supported yet");
        return null;
      } else if (result.stringResult.startsWith('ERROR:')) {
        globalTalker.error("[JsApiEvaler] JavaScript error: ${result.stringResult}");
        return null;
      } else if (result.stringResult == 'FUNCTION_NOT_FOUND') {
        globalTalker.error("[JsApiEvaler] getMusicPlayInfo function not found");
        return null;
      } else if (result.stringResult.isNotEmpty && result.stringResult != 'null') {
        var jsonResult = jsonDecode(result.stringResult);
        return _convertJsResultToPlayInfo(jsonResult);
      }

      return null;
    } catch (e) {
      globalTalker.error("[JsApiEvaler] getMusicPlayInfo failed: $e");
      return null;
    }
  }

  PlayInfo? _convertJsResultToPlayInfo(Map<String, dynamic> jsResult) {
    try {
      // JavaScript API返回的是qualities数组，我们需要选择一个质量
      var qualitiesData = jsResult['qualities'];
      if (qualitiesData == null || qualitiesData is! List) {
        globalTalker.error("[JsApiEvaler] Invalid qualities data from JavaScript API");
        return null;
      }

      var qualities = qualitiesData;
      if (qualities.isEmpty) {
        globalTalker.error("[JsApiEvaler] No qualities available from JavaScript API");
        return null;
      }

      // 选择第一个可用的质量（通常是最高质量）
      var selectedQuality = qualities[0] as Map<String, dynamic>;

      // 创建Quality对象
      Quality quality = Quality(
        short: selectedQuality['summary'] as String? ?? 'standard',
        level: selectedQuality['summary'] as String?,
        bitrate: selectedQuality['bitrate'] as int?,
        format: selectedQuality['format'] as String?,
        size: selectedQuality['size']?.toString(),
      );

      return PlayInfo(
        uri: selectedQuality['url'] as String,
        quality: quality,
      );
    } catch (e) {
      globalTalker.error("[JsApiEvaler] Failed to convert JS result to PlayInfo: $e");
      return null;
    }
  }

  Future<List<MusicInfo>> searchMusic(String keyword, int limit) async {
    try {
      // 调用JavaScript的searchMusic函数
      var result = jsRuntime.evaluate('''
        (function() {
          try {
            if (typeof searchMusic === 'function') {
              var result = searchMusic('${keyword.replaceAll("'", "\\'")}', $limit);
              if (result && typeof result === 'object') {
                return JSON.stringify(result);
              } else if (typeof result === 'string') {
                return result;
              } else {
                return 'FUNCTION_RETURNED_NULL';
              }
            } else {
              return 'FUNCTION_NOT_FOUND';
            }
          } catch (e) {
            return 'ERROR: ' + e.toString();
          }
        })()
      ''');

      // 检查是否包含HTTP请求
      if (result.stringResult.contains('__FLUTTER_HTTP_REQUEST__')) {
        // 处理HTTP请求
        var httpRequestData = result.stringResult.replaceFirst('__FLUTTER_HTTP_REQUEST__', '');
        var requestInfo = jsonDecode(httpRequestData);

        var httpResponse = await _handleHttpRequest(
          requestInfo['url'],
          requestInfo['method'],
          Map<String, String>.from(requestInfo['headers']),
          requestInfo['body']
        );

        if (httpResponse != null) {
          // 将HTTP响应注入到JavaScript环境中，然后重新执行
          jsRuntime.evaluate('''
            var _lastHttpResponse = ${jsonEncode(httpResponse)};

            // 重写httpRequest函数返回真实数据
            function httpRequest(url, options) {
              return {
                ok: true,
                status: 200,
                statusText: 'OK',
                json: function() { return _lastHttpResponse; },
                text: function() { return JSON.stringify(_lastHttpResponse); }
              };
            }
          ''');

          // 重新调用JavaScript函数
          result = jsRuntime.evaluate('''
            (function() {
              try {
                if (typeof searchMusic === 'function') {
                  var result = searchMusic('${keyword.replaceAll("'", "\\'")}', $limit);
                  if (result && typeof result === 'object') {
                    return JSON.stringify(result);
                  } else if (typeof result === 'string') {
                    return result;
                  } else {
                    return 'FUNCTION_RETURNED_NULL';
                  }
                } else {
                  return 'FUNCTION_NOT_FOUND';
                }
              } catch (e) {
                return 'ERROR: ' + e.toString();
              }
            })()
          ''');
        }
      }

      if (result.stringResult.startsWith('ERROR:')) {
        globalTalker.error("[JsApiEvaler] JavaScript error: ${result.stringResult}");
        return [];
      } else if (result.stringResult == 'FUNCTION_NOT_FOUND') {
        globalTalker.error("[JsApiEvaler] searchMusic function not found");
        return [];
      } else if (result.stringResult.isNotEmpty && result.stringResult != 'null' && result.stringResult != 'FUNCTION_RETURNED_NULL') {
        var jsonResult = jsonDecode(result.stringResult) as Map<String, dynamic>;
        var musics = jsonResult['musics'] as List?;
        if (musics != null) {
          return musics.take(limit).map((item) => _convertJsResultToMusicInfo(item)).toList();
        }
      }

      return [];
    } catch (e) {
      globalTalker.error("[JsApiEvaler] searchMusic failed: $e");
      return [];
    }
  }

  // 获取歌词（包括翻译歌词）
  Future<Map<String, String?>> getLyric(String source, String extra) async {
    try {
      // 调用JavaScript函数
      var result = jsRuntime.evaluate('''
        (function() {
          try {
            if (typeof getLyric === 'function') {
              var result = getLyric('$source', '$extra');
              if (result && typeof result === 'object') {
                return JSON.stringify(result);
              } else {
                return 'FUNCTION_RETURNED_NULL';
              }
            } else {
              return 'FUNCTION_NOT_FOUND';
            }
          } catch (e) {
            return 'ERROR: ' + e.toString();
          }
        })()
      ''');

      if (result.stringResult.startsWith('ERROR:')) {
        globalTalker.error("[JsApiEvaler] JavaScript error: ${result.stringResult}");
        return {'lyric': null, 'tlyric': null};
      } else if (result.stringResult == 'FUNCTION_NOT_FOUND') {
        globalTalker.error("[JsApiEvaler] getLyric function not found");
        return {'lyric': null, 'tlyric': null};
      } else if (result.stringResult.isNotEmpty && result.stringResult != 'null' && result.stringResult != 'FUNCTION_RETURNED_NULL') {
        var jsonResult = jsonDecode(result.stringResult);
        return {
          'lyric': jsonResult['lyric'] as String?,
          'tlyric': jsonResult['tlyric'] as String?,
        };
      }

      return {'lyric': null, 'tlyric': null};
    } catch (e) {
      globalTalker.error("[JsApiEvaler] getLyric failed: $e");
      return {'lyric': null, 'tlyric': null};
    }
  }

  MusicInfo _convertJsResultToMusicInfo(Map<String, dynamic> jsResult) {
    try {
      // 处理artist字段 - 可能是字符串或数组
      List<String> artistList = [];
      var artistData = jsResult['artist'];
      if (artistData is String) {
        artistList = [artistData];
      } else if (artistData is List) {
        artistList = artistData.cast<String>();
      }

      // 处理ID字段 - 可能是字符串或整数
      int musicId = 0;
      var idData = jsResult['id'];
      if (idData is String) {
        musicId = idData.hashCode; // 将字符串ID转换为整数
      } else if (idData is int) {
        musicId = idData;
      }

      return MusicInfo(
        id: musicId,
        name: jsResult['name'] as String? ?? '',
        artist: artistList,
        album: jsResult['album'] as String?,
        artPic: jsResult['pic_url'] as String?, // JavaScript API使用pic_url字段
        duration: jsResult['duration'] as int?,
        source: 'js', // 固定为js源
        lyric: jsResult['lyric'] as String?,
        tlyric: jsResult['tlyric'] as String?,
        qualities: [], // 搜索结果中不包含质量信息，播放时再获取
      );
    } catch (e) {
      globalTalker.error("[JsApiEvaler] Failed to convert JS result to MusicInfo: $e");
      return MusicInfo(
        id: 0,
        name: 'Unknown',
        artist: [],
        album: null,
        artPic: null,
        duration: null,
        source: 'js',
        qualities: [],
      );
    }
  }

  // 获取可用音质选项
  Future<List<Map<String, dynamic>>> getAvailableQualities() async {
    try {
      // 调用JavaScript函数
      var result = jsRuntime.evaluate('''
        (function() {
          try {
            if (typeof getAvailableQualities === 'function') {
              var result = getAvailableQualities();
              if (result && typeof result === 'object') {
                return JSON.stringify(result);
              } else {
                return 'FUNCTION_RETURNED_NULL';
              }
            } else {
              return 'FUNCTION_NOT_FOUND';
            }
          } catch (e) {
            return 'ERROR: ' + e.toString();
          }
        })()
      ''');

      if (result.stringResult.startsWith('ERROR:')) {
        globalTalker.error("[JsApiEvaler] JavaScript error: ${result.stringResult}");
        return _getDefaultQualities();
      } else if (result.stringResult == 'FUNCTION_NOT_FOUND') {
        globalTalker.error("[JsApiEvaler] getAvailableQualities function not found");
        return _getDefaultQualities();
      } else if (result.stringResult.isNotEmpty && result.stringResult != 'null' && result.stringResult != 'FUNCTION_RETURNED_NULL') {
        var jsonResult = jsonDecode(result.stringResult);
        if (jsonResult is List) {
          return List<Map<String, dynamic>>.from(jsonResult);
        }
      }

      return _getDefaultQualities();
    } catch (e) {
      globalTalker.error("[JsApiEvaler] getAvailableQualities failed: $e");
      return _getDefaultQualities();
    }
  }

  List<Map<String, dynamic>> _getDefaultQualities() {
    return [
      {'summary': '标准', 'bitrate': 128, 'format': 'mp3'},
      {'summary': '高品质', 'bitrate': 320, 'format': 'mp3'},
    ];
  }

  void dispose() {
    jsRuntime.dispose();
  }
}

// JavaScript音乐源配置类
class JsApi {
  final String? url;
  final String localPath;
  final String? lastHash;
  final DateTime? lastModifiedTime;

  JsApi({
    this.url,
    required this.localPath,
    this.lastHash,
    this.lastModifiedTime,
  });

  static Future<JsApi> fromPath({required String path}) async {
    var file = File(path);
    var content = await file.readAsString();
    var hash = _calculateHash(content);
    var lastModified = await file.lastModified();

    return JsApi(
      localPath: path,
      lastHash: hash,
      lastModifiedTime: lastModified,
    );
  }

  static Future<JsApi> fromUrl({required String url}) async {
    try {
      // 创建HTTP客户端
      var client = HttpClient();
      client.userAgent = 'AppRhyme/1.0';

      // 发起HTTP请求
      var request = await client.getUrl(Uri.parse(url));
      var response = await request.close();

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: Failed to download from $url');
      }

      // 读取响应内容
      var bytes = await response.fold<List<int>>(<int>[], (previous, element) => previous..addAll(element));
      var content = utf8.decode(bytes);

      // 验证是否为JavaScript文件
      if (!url.endsWith('.js') && !content.contains('function') && !content.contains('const') && !content.contains('var')) {
        throw Exception('Downloaded content does not appear to be a JavaScript file');
      }

      // 生成本地文件路径
      var fileName = url.split('/').last;
      if (!fileName.endsWith('.js')) {
        fileName = '$fileName.js';
      }
      var localPath = '$globalDocumentPath/js_sources/$fileName';

      // 确保目录存在
      var directory = Directory('$globalDocumentPath/js_sources');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // 保存到本地文件
      var file = File(localPath);
      await file.writeAsString(content);

      // 计算哈希值
      var hash = _calculateHash(content);
      var lastModified = await file.lastModified();

      client.close();

      return JsApi(
        url: url,
        localPath: localPath,
        lastHash: hash,
        lastModifiedTime: lastModified,
      );
    } catch (e) {
      throw Exception('Failed to download JavaScript music source from URL: $e');
    }
  }

  static String _calculateHash(String content) {
    var bytes = utf8.encode(content);
    var digest = crypto_lib.sha256.convert(bytes);
    return digest.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JsApi &&
        other.url == url &&
        other.localPath == localPath &&
        other.lastHash == lastHash;
  }

  @override
  int get hashCode {
    return url.hashCode ^ localPath.hashCode ^ lastHash.hashCode;
  }
}
