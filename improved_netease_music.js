/**
 * Improved NetEase Music JS Source
 * Based on multiple APIs with fallback support
 * Author: AppRhyme
 * Version: 2.0.0
 */

// Configuration
const CONFIG = {
  name: 'Improved NetEase Music',
  version: '2.0.0',
  author: 'AppRhyme',
  description: 'NetEase Music source with multiple API fallbacks',
  apis: [
    'https://api.kxzjoker.cn',
    'https://netease-cloud-music-api-psi-wine.vercel.app',
    'https://music.163.com'
  ]
};

// 当前使用的API索引
let currentApiIndex = 0;

/**
 * HTTP request helper function with retry mechanism
 */
async function httpRequest(url, options = {}) {
  const maxRetries = 3;
  let lastError;
  
  for (let retry = 0; retry < maxRetries; retry++) {
    try {
      console.log(`Making request to: ${url} (attempt ${retry + 1})`);
      
      // 模拟fetch请求 - 在实际环境中这会被Flutter端拦截
      const response = await fetch(url, {
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://music.163.com/',
          'Accept': 'application/json',
          ...options.headers
        },
        body: options.body
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Request successful: ${url}`);
      return data;
    } catch (error) {
      console.error(`Request failed (attempt ${retry + 1}): ${error.message}`);
      lastError = error;
      
      // 如果是网络错误，尝试下一个API
      if (retry === maxRetries - 1 && currentApiIndex < CONFIG.apis.length - 1) {
        currentApiIndex++;
        console.log(`Switching to API: ${CONFIG.apis[currentApiIndex]}`);
      }
    }
  }
  
  throw lastError || new Error('All retry attempts failed');
}

/**
 * Get current API base URL
 */
function getCurrentApiUrl() {
  return CONFIG.apis[currentApiIndex];
}

/**
 * Search music
 * @param {string} keyword - Search keyword
 * @param {number} limit - Result limit
 * @returns {Promise<Object>} Search results
 */
async function searchMusic(keyword, limit = 20) {
  try {
    console.log(`Search music: ${keyword}, limit: ${limit}`);

    // 尝试使用kxzjoker API
    let url = `${getCurrentApiUrl()}/api/163_search?name=${encodeURIComponent(keyword)}&limit=${limit}`;
    let response = await httpRequest(url);

    if (!response.data || response.code !== 200) {
      // 如果第一个API失败，尝试官方API格式
      url = `${getCurrentApiUrl()}/search?keywords=${encodeURIComponent(keyword)}&limit=${limit}&type=1`;
      response = await httpRequest(url);
    }

    let songs = [];
    
    // 处理不同API的响应格式
    if (response.result && response.result.songs) {
      // 官方API格式
      songs = response.result.songs;
    } else if (response.data && Array.isArray(response.data)) {
      // kxzjoker API格式
      songs = response.data;
    }

    const musics = songs.map(song => {
      // 解析时长
      let durationMs = 0;
      if (song.duration) {
        if (typeof song.duration === 'number') {
          durationMs = song.duration;
        } else if (typeof song.duration === 'string') {
          const parts = song.duration.split(':');
          if (parts.length === 2) {
            durationMs = (parseInt(parts[0]) * 60 + parseInt(parts[1])) * 1000;
          }
        }
      } else if (song.dt) {
        durationMs = song.dt;
      }

      // 处理艺术家信息
      let artistNames = [];
      if (song.artists && Array.isArray(song.artists)) {
        artistNames = song.artists.map(artist => artist.name);
      } else if (song.artist && Array.isArray(song.artist)) {
        artistNames = song.artist.map(artist => artist.name);
      } else if (typeof song.artist === 'string') {
        artistNames = [song.artist];
      }

      // 处理专辑信息
      let albumName = '';
      if (song.album && song.album.name) {
        albumName = song.album.name;
      } else if (typeof song.album === 'string') {
        albumName = song.album;
      }

      // 生成封面URL
      let picUrl = '';
      if (song.album && song.album.picUrl) {
        picUrl = song.album.picUrl;
      } else if (song.album && song.album.id) {
        picUrl = `https://p1.music.126.net/${song.album.id}/109951167791456650.jpg`;
      } else if (song.id) {
        picUrl = `https://p1.music.126.net/${song.id}/109951167791456650.jpg`;
      }

      return {
        id: song.id.toString(),
        name: song.name || '未知歌曲',
        artist: artistNames.join(', '),
        album: albumName,
        duration: durationMs,
        pic_url: picUrl,
        source: 'js'
      };
    });

    console.log(`Found ${musics.length} songs for keyword: ${keyword}`);
    return {
      musics: musics,
      total: musics.length,
      page: 1
    };

  } catch (error) {
    console.error(`Search music failed: ${error.message}`);
    return {
      musics: [],
      total: 0,
      page: 1
    };
  }
}

/**
 * Get music play information
 * @param {string} source - Music source JSON string
 * @param {string} extra - Extra information (contains quality level)
 * @returns {Promise<Object|null>} Play information
 */
async function getMusicPlayInfo(source, extra = '{}') {
  try {
    console.log(`Get play info: source=${source}, extra=${extra}`);

    const musicInfo = JSON.parse(source);
    const musicId = musicInfo.id;

    // 解析extra参数获取音质等级
    let extraParams = {};
    try {
      extraParams = JSON.parse(extra);
    } catch (e) {
      extraParams = { quality: 'standard' };
    }
    
    const level = extraParams.quality || extraParams.level || 'standard';

    // 尝试获取播放链接
    let url = `${getCurrentApiUrl()}/api/163_url?id=${musicId}&level=${level}`;
    let response = await httpRequest(url);

    if (!response.data || response.code !== 200) {
      // 尝试官方API格式
      url = `${getCurrentApiUrl()}/song/url?id=${musicId}&br=${getBitrateFromLevel(level)}`;
      response = await httpRequest(url);
    }

    let songData = null;
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      songData = response.data[0];
    } else if (response.data && !Array.isArray(response.data)) {
      songData = response.data;
    }

    if (!songData || !songData.url) {
      throw new Error('Song cannot be played (may require VIP or not available)');
    }

    // 根据level和比特率确定音质描述
    const bitrate = songData.br || getBitrateFromLevel(level);
    const qualitySummary = getQualityName(level, bitrate);

    return {
      qualities: [
        {
          summary: qualitySummary,
          bitrate: bitrate,
          format: songData.type || getFormatFromLevel(level),
          size: songData.size || 0,
          url: songData.url
        }
      ]
    };

  } catch (error) {
    console.error(`Get play info failed: ${error.message}`);
    return null;
  }
}

/**
 * Get lyric information
 * @param {string} source - Music source JSON string
 * @param {string} extra - Extra information
 * @returns {Promise<Object|null>} Lyric information
 */
async function getLyric(source, extra = '{}') {
  try {
    console.log(`Get lyrics: source=${source}, extra=${extra}`);

    const musicInfo = JSON.parse(source);
    const musicId = musicInfo.id;

    let url = `${getCurrentApiUrl()}/api/163_lyric?id=${musicId}`;
    let response = await httpRequest(url);

    if (!response.data || response.code !== 200) {
      // 尝试官方API格式
      url = `${getCurrentApiUrl()}/lyric?id=${musicId}`;
      response = await httpRequest(url);
    }

    let lyricText = '[00:00.00]暂无歌词';
    let tlyricText = null;

    if (response.data) {
      lyricText = response.data.lrc || response.data.lyric || lyricText;
      tlyricText = response.data.tlyric || response.data.tlyric || null;
    } else if (response.lrc && response.lrc.lyric) {
      lyricText = response.lrc.lyric;
      if (response.tlyric && response.tlyric.lyric) {
        tlyricText = response.tlyric.lyric;
      }
    }

    return {
      lyric: lyricText,
      tlyric: tlyricText
    };

  } catch (error) {
    console.error(`Get lyrics failed: ${error.message}`);
    return {
      lyric: '[00:00.00]获取歌词失败',
      tlyric: null
    };
  }
}

/**
 * Get available qualities
 * @returns {Array} Available quality options
 */
function getAvailableQualities() {
  return [
    {
      summary: "标准",
      bitrate: 128,
      format: "mp3",
      level: "standard"
    },
    {
      summary: "高品质",
      bitrate: 320,
      format: "mp3",
      level: "higher"
    },
    {
      summary: "极高品质",
      bitrate: 320,
      format: "mp3",
      level: "exhigh"
    },
    {
      summary: "无损音质",
      bitrate: 999,
      format: "flac",
      level: "lossless"
    }
  ];
}

// Helper functions
function getBitrateFromLevel(level) {
  switch (level) {
    case 'standard': return 128000;
    case 'higher': return 192000;
    case 'exhigh': return 320000;
    case 'lossless': return 999000;
    case 'hires': return 1411000;
    default: return 128000;
  }
}

function getFormatFromLevel(level) {
  switch (level) {
    case 'lossless':
    case 'hires': return 'flac';
    default: return 'mp3';
  }
}

function getQualityName(level, bitrate) {
  switch (level) {
    case 'standard': return '标准';
    case 'higher': return '高品质';
    case 'exhigh': return '极高品质';
    case 'lossless': return '无损音质';
    case 'hires': return 'Hi-Res音质';
    default:
      if (bitrate >= 320000) return '极高品质';
      if (bitrate >= 192000) return '高品质';
      return '标准';
  }
}

// Export functions for Flutter to call
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    CONFIG,
    searchMusic,
    getMusicPlayInfo,
    getLyric,
    getAvailableQualities
  };
}

console.log(`${CONFIG.name} v${CONFIG.version} loaded successfully`);
