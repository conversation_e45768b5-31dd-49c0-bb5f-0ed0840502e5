// 改进的测试JavaScript音乐源
// 包含完整的API实现和错误处理

// 音乐源配置
const CONFIG = {
    name: "改进测试音乐源",
    version: "1.1.0",
    author: "AppRhyme Team",
    baseUrl: "https://example.com/api"
};

// 模拟音乐数据库
const MOCK_MUSIC_DATABASE = [
    {
        id: "test_001",
        name: "测试歌曲1",
        artist: "测试歌手1",
        album: "测试专辑1",
        pic_url: "https://example.com/pic1.jpg",
        duration: 240000,
        lyric: "[00:00.00]测试歌曲1\n[00:05.00]演唱：测试歌手1\n[00:10.00]这是第一首测试歌曲",
        tlyric: "[00:00.00]Test Song 1\n[00:05.00]Singer: Test Artist 1\n[00:10.00]This is the first test song"
    },
    {
        id: "test_002", 
        name: "测试歌曲2",
        artist: "测试歌手2",
        album: "测试专辑2",
        pic_url: "https://example.com/pic2.jpg",
        duration: 180000,
        lyric: "[00:00.00]测试歌曲2\n[00:05.00]演唱：测试歌手2\n[00:10.00]这是第二首测试歌曲",
        tlyric: "[00:00.00]Test Song 2\n[00:05.00]Singer: Test Artist 2\n[00:10.00]This is the second test song"
    },
    {
        id: "test_003",
        name: "Sunshine Girl",
        artist: "测试歌手3",
        album: "阳光专辑",
        pic_url: "https://example.com/pic3.jpg",
        duration: 210000,
        lyric: "[00:00.00]Sunshine Girl\n[00:05.00]演唱：测试歌手3\n[00:10.00]阳光女孩的故事",
        tlyric: "[00:00.00]Sunshine Girl\n[00:05.00]Singer: Test Artist 3\n[00:10.00]Story of the sunshine girl"
    },
    {
        id: "test_004",
        name: "The Royal Wedding Song",
        artist: "皇室乐队",
        album: "皇室典礼",
        pic_url: "https://example.com/pic4.jpg",
        duration: 300000,
        lyric: "[00:00.00]The Royal Wedding Song\n[00:05.00]演唱：皇室乐队\n[00:10.00]皇室婚礼之歌",
        tlyric: "[00:00.00]The Royal Wedding Song\n[00:05.00]Singer: Royal Band\n[00:10.00]Song for the royal wedding"
    }
];

// 搜索音乐
function searchMusic(keyword, page) {
    console.log('searchMusic called with:', keyword, page);
    
    try {
        // 模拟搜索逻辑
        var results = MOCK_MUSIC_DATABASE.filter(function(music) {
            return music.name.toLowerCase().indexOf(keyword.toLowerCase()) !== -1 ||
                   music.artist.toLowerCase().indexOf(keyword.toLowerCase()) !== -1 ||
                   music.album.toLowerCase().indexOf(keyword.toLowerCase()) !== -1;
        });
        
        // 分页处理
        var pageSize = 20;
        var startIndex = (page - 1) * pageSize;
        var endIndex = startIndex + pageSize;
        var pagedResults = results.slice(startIndex, endIndex);
        
        return {
            musics: pagedResults,
            total: results.length,
            page: page || 1
        };
    } catch (error) {
        console.error('搜索音乐失败:', error);
        return { musics: [], total: 0, page: page || 1 };
    }
}

// 获取音乐播放信息
function getMusicPlayInfo(source, extra) {
    console.log('getMusicPlayInfo called with:', source, extra);
    
    try {
        var musicInfo = JSON.parse(source);
        
        // 根据音乐ID生成播放链接
        var baseUrl = "https://example.com/music/";
        
        return {
            qualities: [
                {
                    summary: "高品质",
                    bitrate: 320,
                    format: "mp3",
                    size: 8000000,
                    url: baseUrl + musicInfo.id + "_320.mp3"
                },
                {
                    summary: "标准",
                    bitrate: 128,
                    format: "mp3", 
                    size: 3200000,
                    url: baseUrl + musicInfo.id + "_128.mp3"
                }
            ]
        };
    } catch (error) {
        console.error('获取播放信息失败:', error);
        return null;
    }
}

// 获取歌词
function getLyric(source, extra) {
    console.log('getLyric called with:', source, extra);
    
    try {
        var musicInfo = JSON.parse(source);
        
        // 从模拟数据库中查找歌词
        var music = MOCK_MUSIC_DATABASE.find(function(item) {
            return item.id === musicInfo.id || item.name === musicInfo.name;
        });
        
        if (music) {
            return {
                lyric: music.lyric,
                tlyric: music.tlyric
            };
        } else {
            return {
                lyric: "[00:00.00]未找到歌词",
                tlyric: "[00:00.00]Lyrics not found"
            };
        }
    } catch (error) {
        console.error('获取歌词失败:', error);
        return {
            lyric: "[00:00.00]获取歌词失败",
            tlyric: "[00:00.00]Failed to get lyrics"
        };
    }
}

// 获取可用音质列表
function getAvailableQualities() {
    return [
        {
            summary: "高品质",
            bitrate: 320,
            format: "mp3"
        },
        {
            summary: "标准",
            bitrate: 128,
            format: "mp3"
        }
    ];
}

// 初始化函数
function initialize() {
    console.log(CONFIG.name + ' v' + CONFIG.version + ' 初始化完成');
    console.log('作者: ' + CONFIG.author);
    console.log('基础URL: ' + CONFIG.baseUrl);
}

// 清理函数
function cleanup() {
    console.log(CONFIG.name + ' 清理完成');
}

// 自动初始化
initialize();

console.log('JavaScript音乐源加载完成: ' + CONFIG.name + ' v' + CONFIG.version);
